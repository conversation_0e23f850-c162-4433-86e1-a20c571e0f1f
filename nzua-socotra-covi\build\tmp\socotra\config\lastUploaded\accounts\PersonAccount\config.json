{"displayName": "Person", "abstract": false, "data": {"personFirstName": {"displayName": "First Name", "defaultValue": "N/A", "type": "string", "maxLength": 20000, "scope": "P"}, "personLastName": {"displayName": "Last Name", "defaultValue": "N/A", "type": "string", "minLength": 0, "maxLength": 20000, "scope": "P"}, "personSalutation": {"displayName": "Salutation", "type": "string?", "maxLength": 20000, "options": ["Dr.", "Mx.", "Ms.", "Prof.", "Mr.", "Miss.", "Mrs."], "scope": "P"}, "personMiddleName": {"displayName": "Middle Name(s)", "type": "string?", "maxLength": 20000, "scope": "P"}, "personDOB": {"displayName": "Date of Birth", "type": "date"}, "personAge": {"displayName": "Age", "type": "int", "scope": "P"}, "custAcctMgmtDistributionChannel": {"displayName": "Distribution Channel", "type": "string", "maxLength": 0, "options": ["Direct ", "Broker"], "scope": "P"}, "policyHolderPreviousInsurer": {"displayName": "Previous Insurer", "type": "string?"}, "policyHolderPreviousInsurerOther": {"displayName": "Previous Insurer - Other", "type": "string?", "maxLength": 20000, "scope": "P"}, "customerVulnerability": {"displayName": "Customer Vulnerability", "type": "CustomerVulnerability?", "scope": "P"}, "custromerAccountLifecycle": {"displayName": "Customer Account Lifecycle", "type": "CustomerAccountLifecycle", "scope": "P"}, "accountBroker": {"displayName": "Account Broker", "type": "AccountBroker?", "scope": "P"}, "accountReferralPartner": {"type": "Account<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ner?", "scope": "P", "displayName": "Referral Partner"}}}