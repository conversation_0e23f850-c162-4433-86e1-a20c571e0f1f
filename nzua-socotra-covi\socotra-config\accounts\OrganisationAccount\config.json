{"displayName": "Organisation", "abstract": false, "data": {"organisationName": {"displayName": "Organisation Name", "type": "string", "scope": "P", "defaultValue": "N/A", "maxLength": 20000}, "organisationTradingName": {"displayName": "Trading Name", "type": "string?", "scope": "P", "maxLength": 20000}, "organisationNZBN": {"displayName": "NZBN (New Zealand Business Number)", "type": "int?", "scope": "P"}, "organisationDateofIncorporation": {"displayName": "Date of Incorporation", "type": "date?", "scope": "P"}, "organisationContactPerson": {"displayName": "Contact Person", "type": "string?", "scope": "P", "maxLength": 20000}, "custAcctMgmtDistributionChannel": {"displayName": "Distribution Channel", "type": "string", "scope": "P", "maxLength": 20000, "options": ["Direct", "Broker"]}, "policyHolderPreviousInsurer": {"displayName": "Previous Insurer", "type": "string?", "scope": "P", "maxLength": 20000}, "policyHolderPreviousInsurerOther": {"displayName": "Previous Insurer - Other", "type": "string?", "scope": "P", "maxLength": 20000}, "customerVulnerability": {"displayName": "Customer Vulnerability", "type": "CustomerVulnerability?", "scope": "P"}, "customerAccountLifecycle": {"displayName": "Customer Account Lifecycle", "type": "CustomerAccountLifecycle", "scope": "P"}, "accountBroker": {"displayName": "Account Broker", "type": "AccountBroker?", "scope": "P"}, "accountReferralPartner": {"displayName": "Referral Partner", "type": "Account<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ner?", "scope": "P"}, "contactInformation": {"displayName": "Contact Information", "type": "ContactInformation", "scope": "P"}, "customerGroup": {"displayName": "Customer Group", "type": "CustomerGroup*", "scope": "P"}}}