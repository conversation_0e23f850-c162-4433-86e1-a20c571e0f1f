package com.socotra.deployment.customer;

import com.socotra.coremodel.*;
import com.socotra.deployment.*;
import com.socotra.deployment.customer.ValidationPlugin.PersonAccountRequest;



public class PreCommitPluginImpl implements PreCommitPlugin {

    @Override
    public PersonAccount preCommit(PersonAccountRequest request ){
       
        //Check if No Fixed Address under Contact information is FALSE then change the addressLine1 quantifier to required
       

    }

     @Override
    public OrganisationAccount preCommit(OrganisationAccountRequest request ){
        
        //Check if No Fixed Address under Contact information is FALSE then change the addressLine1 quantifier to required
      

    }






    
}
