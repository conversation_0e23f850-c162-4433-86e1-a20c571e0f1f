{"displayName": "Contact Information", "abstract": false, "data": {"noFixedAddress": {"displayName": "No Fixed Address", "type": "boolean", "scope": "P"}, "addressLine1": {"displayName": "Address Line 1", "type": "string?", "scope": "P", "maxLength": 20000}, "addressLine2": {"displayName": "Address Line 2", "type": "string?", "scope": "P", "maxLength": 20000}, "addressSuburbSuburb": {"displayName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "type": "string?", "scope": "P", "maxLength": 20000}, "addressCityRegion": {"displayName": "Address - City/Region", "type": "string?", "scope": "P", "maxLength": 20000}, "addressPostCode": {"displayName": "Address - Post Code", "type": "int?", "scope": "P", "max": "4"}, "addressCountry": {"displayName": "Address - Country", "type": "string?", "scope": "P", "maxLength": 20000}, "custEmailAddress": {"displayName": "Email Address", "type": "string*", "scope": "P", "maxLength": 20000}, "custMobilePhoneNumber": {"displayName": "Mobile Phone Number", "type": "string*", "scope": "P", "maxLength": 20000}, "custPhoneNumber": {"displayName": "Landline Phone Number", "type": "string*", "scope": "P", "maxLength": 20000}}}