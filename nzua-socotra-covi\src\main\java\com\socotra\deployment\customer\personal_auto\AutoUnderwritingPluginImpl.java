package com.socotra.deployment.customer.personal_auto;
import com.socotra.deployment.customer.*;
import java.util.*;

import com.socotra.coremodel.*;
import com.socotra.deployment.DataFetcher;
import com.socotra.platform.tools.ULID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AutoUnderwritingPluginImpl implements UnderwritingPlugin {
    private static final Logger log = LoggerFactory.getLogger(AutoUnderwritingPluginImpl.class);

    @Override
    public UnderwritingModification underwrite(Personal_autoQuoteRequest request) {
        Personal_autoQuote quote = request.quote();

        

        //  VERSION -1

        quote = DataFetcher.getInstance().getQuote(quote.locator());

        VehicleQuote vq = quote.vehicle();
        VehicleQuote.VehicleQuoteData vehicleQuoteData = vq.data();
        Personal_autoQuote.Personal_autoQuoteData motorData = quote.data();

        // Motor Product Data
        String license_canceled = motorData.license_canceled();
        String insurance_denied = motorData.insurance_denied();
        //List<Policyholder> policyholders = motorData.policyholder();

        // Vehicle Exposure Information
        int vehicle_value = vehicleQuoteData.vehicle_value();
        int vehicle_accessories_value = vehicleQuoteData.vehicle_accessoriesvalue();
        String vehicle_use = vehicleQuoteData.vehicle_use();
        String vehicle_year = vehicleQuoteData.vehicle_year();
        String vehicle_license_plate = vehicleQuoteData.vehicle_license_plate();
        String vehicle_damage = vehicleQuoteData.vehicle_damage();

        // Underwriting rules
        // 1. License Cancelled
        if (license_canceled.equals("Yes")) {
            return UnderwritingModification.builder()
                    .flagsToCreate(List.of(UnderwritingFlagCore.builder()
                            .level(UnderwritingLevel.decline)
                            .note("Due to your previous insurance history, we are unable to offer you insurance")
                            .elementLocator(quote.locator())
                            .build()))
                    .build();
        }

        // 2. Vehicle value exceeds threshold
        // Check if the sum of values exceeds 100000

        if (vehicle_value + vehicle_accessories_value > 1000000) {
            return UnderwritingModification.builder()
                    .flagsToCreate(List.of(UnderwritingFlagCore.builder()
                            .level(UnderwritingLevel.block)
                            .note("Due to the value of your car registration number " + vehicle_license_plate + " , we are unable to offer you insurance")
                            .elementLocator(vq.locator())
                            .build()))
                    .build();
        }

        // 3. Vehicle use and age
        // If vehicle use is business or business and personal and vehicle age is > 34, decline

        if ((vehicle_use.equals("Business use only") || vehicle_use.equals("Personal and business use")) && Integer.parseInt(vehicle_year) < 1990) {
            return UnderwritingModification.builder()
                    .flagsToCreate(List.of(UnderwritingFlagCore.builder()
                            .level(UnderwritingLevel.decline)
                            .note("We don't insure cars used for business purpose with registration number" + vehicle_license_plate + "and who are older than 34 years")
                            .elementLocator(vq.locator())
                            .build()))
                    .build();
        }


        // 4. Vehicle damage and insurance denied
        // No insurance for cars that have previous hail damage and have been declined insurance in the pas

        if (vehicle_damage.equals("Hail Damage") && insurance_denied.equals("Yes")){
            return UnderwritingModification.builder()
                    .flagsToCreate(List.of(UnderwritingFlagCore.builder()
                            .level(UnderwritingLevel.block)
                            .note("We don't insure cars that have previous hail damage and have been declined insurance in the past")
                            .elementLocator(vq.locator())
                            .build()))
                    .build();
        }


        return UnderwritingModification.builder()
                .flagsToCreate(List.of(UnderwritingFlagCore.builder()
                        .level(UnderwritingLevel.approve)
                        .build()))
                .build();
    }
}