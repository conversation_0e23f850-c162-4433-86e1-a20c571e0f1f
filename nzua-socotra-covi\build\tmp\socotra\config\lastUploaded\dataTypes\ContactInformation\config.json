{"abstract": false, "displayName": "Contact Information", "data": {"noFixedAddress": {"displayName": "No Fixed Address", "type": "boolean", "scope": "P"}, "addressLine1": {"displayName": "Address Line 1", "type": "string?", "maxLength": 20000, "scope": "P"}, "addressLine2": {"displayName": "Address Line 2", "type": "string?", "maxLength": 20000, "scope": "P"}, "addressSuburbSuburb": {"displayName": "<PERSON><PERSON> - <PERSON><PERSON><PERSON>", "type": "string?", "maxLength": 20000, "scope": "P"}, "addressCityRegion": {"displayName": "Address - City/Region", "type": "string?", "maxLength": 20000, "scope": "P"}, "addressPostCode": {"displayName": "Address - Post Code", "type": "int?", "max": "4", "scope": "P"}, "addressCountry": {"displayName": "Address - Country", "type": "string?", "maxLength": 20000, "scope": "P"}, "custEmailAddress": {"displayName": "Email Address", "type": "string*", "maxLength": 20000, "scope": "P"}, "custMobilePhoneNumber": {"displayName": "Mobile Phone Number", "type": "string*", "maxLength": 20000, "scope": "P"}, "custPhoneNumber": {"displayName": "Landline Phone Number", "type": "string*", "maxLength": 20000, "scope": "P"}}}