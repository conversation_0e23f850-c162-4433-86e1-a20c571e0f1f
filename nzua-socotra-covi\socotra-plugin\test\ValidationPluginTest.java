package com.socotra.deployment.customer;

import org.junit.jupiter.api.Test;


import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class ValidationPluginTest {

    @Test
    public void testPersonValidationCatchesSpeciousClaimsToBeTheDarkKnight(){
        var personAccountRequest = mock(ValidationPlugin.PersonAccountRequest.class);
        var personAccount = mock(PersonAccount.class);
        var personAccountData = mock(PersonAccount.PersonAccountData.class);

        when(personAccountRequest.account()).thenReturn(personAccount);
        when(personAccount.data()).thenReturn(personAccountData);

        //Batman return an error
        when(personAccountData.personLastName()).thenReturn("Batman");
        var validationItem = (new ValidationPluginImpl().validate(personAccountRequest));
        assert(validationItem.errors().size() == 1);

        //other names are acceptable
        when(personAccountData.personLastName()).thenReturn("Smith");
        validationItem = (new ValidationPluginImpl().validate(personAccountRequest));
        assert(validationItem.errors().size() == 0);
    }
}
