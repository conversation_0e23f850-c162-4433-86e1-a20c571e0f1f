package com.socotra.deployment.customer;

import com.socotra.coremodel.*;
import com.socotra.deployment.*;
import com.socotra.deployment.customer.ValidationPlugin.PersonAccountRequest;



public class PreCommitPluginImpl implements PreCommitPlugin {

    @Override
    public PersonAccount preCommit(PersonAccountRequest request ){

        PersonAccount account = request.account();
        //Check if No Fixed Address under Contact information is FALSE then change the addressLine1 quantifier to required
       

   


   return account;  //temporary
   



    
}
}