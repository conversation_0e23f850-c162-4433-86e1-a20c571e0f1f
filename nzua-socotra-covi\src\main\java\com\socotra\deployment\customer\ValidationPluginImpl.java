package com.socotra.deployment.customer;

import com.socotra.coremodel.ValidationItem;

public class ValidationPluginImpl implements ValidationPlugin {


 @Override
 public ValidationItem validate(PersonAccountRequest request) {
   PersonAccount personAccount = request.account();
    // Check if the last name - Batman
   if (personAccount.data().personLastName().equalsIgnoreCase("Batman")) {
         return ValidationItem.build()
         .addError("A fictional person cannot be insured")    //error message does not show up in the UI
         .build();
   }


   return ValidationItem.builder().build();
 }


 @Override
 public ValidationItem validate(PersonAccountRequest request) {
   PersonAccount personAccount = request.account();
  //check if nofixed address is flase then change the addressLine1 quantifier to required
  
  
   return ValidationItem.builder().build();
 }


}

