package com.socotra.deployment.customer;

import com.socotra.coremodel.ValidationItem;

public class ValidationPluginImpl implements ValidationPlugin {
 @Override
 public ValidationItem validate(PersonAccountRequest request) {
   PersonAccount consumerAccount = request.account();


   if (consumerAccount.data().personLastName().equalsIgnoreCase("Batman")) {
     return ValidationItem.builder()
         .addError("A fictional person cannot be insured")
         .build();
   }


   return ValidationItem.builder().build();
 }
}