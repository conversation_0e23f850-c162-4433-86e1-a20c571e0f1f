{"displayName": "Account Broker", "abstract": false, "data": {"custAcctMgmtBroker": {"displayName": "Broker Name", "type": "string?", "scope": "P", "maxLength": 20000}, "custAcctMgmtBrokerBranch": {"displayName": "Broker Branch", "type": "string?", "scope": "P", "maxLength": 20000}, "custAcctMgmtBrokerCompany": {"displayName": "Broker Company", "type": "string?", "scope": "P", "maxLength": 20000}, "custAcctMgmtBrokerStartDate": {"displayName": "Broker Start Date", "type": "date?", "scope": "P"}, "custAcctMgmtBrokerEndDate": {"displayName": "Broker End Date", "type": "date?", "scope": "P"}, "brokerEmail": {"displayName": "Broker <PERSON>", "type": "string?", "scope": "P", "maxLength": 20000}, "brokerPhone": {"displayName": "Broker Phone", "type": "string?", "scope": "P", "maxLength": 20000}, "policyHolderHoldingBroker": {"displayName": "Holding Broker", "type": "string?", "scope": "P", "maxLength": 20000}, "policyHolderBrokerLOA": {"displayName": "Broker Letter of Appointment", "type": "boolean?", "scope": "P"}}}