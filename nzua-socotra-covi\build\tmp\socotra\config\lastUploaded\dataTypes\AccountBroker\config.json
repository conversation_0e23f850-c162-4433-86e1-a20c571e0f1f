{"abstract": false, "displayName": "Account Broker", "data": {"custAcctMgmtBroker": {"displayName": "Broker Name", "type": "string?", "maxLength": 20000, "scope": "P"}, "custAcctMgmtBrokerBranch": {"displayName": "Broker Branch", "type": "string?", "maxLength": 20000, "scope": "P"}, "custAcctMgmtBrokerCompany": {"displayName": "Broker Company", "type": "string?", "maxLength": 20000, "scope": "P"}, "custAcctMgmtBrokerStartDate": {"displayName": "Broker Start Date", "type": "date?", "scope": "P"}, "custAcctMgmtBrokerEndDate": {"displayName": "Broker End Date", "type": "date?", "scope": "P"}, "brokerEmail": {"displayName": "Broker <PERSON>", "type": "string?", "maxLength": 20000, "scope": "P"}, "brokerPhone": {"displayName": "Broker Phone", "type": "string?", "maxLength": 20000, "scope": "P"}, "policyHolderHoldingBroker": {"displayName": "Holding Broker", "type": "string?", "maxLength": 20000, "scope": "P"}, "policyHolderBrokerLOA": {"displayName": "Broker Letter of Appointment", "type": "boolean?", "scope": "P"}}}