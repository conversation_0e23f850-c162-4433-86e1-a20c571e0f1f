package com.socotra.deployment.customer.personal_auto;
import com.socotra.deployment.customer.*;

import com.socotra.coremodel.RatingItem;
import com.socotra.coremodel.RatingSet;
import com.socotra.deployment.ResourceSelectorFactory;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class AutoRatingPlugin implements RatePlugin {


    private double exposureRateFire(Personal_autoQuote personal_autoQuote) {

        var ECQuoteVehicle = personal_autoQuote.vehicle();
        var ECQuote = personal_autoQuote.data();

        // examine coverages on each exposure
        var fireCoverage = ECQuoteVehicle.fire();

        // Base rate values
        Optional<BaseRate> baseRateFire = null; // Declare baseRateFire outside the if block

        if(fireCoverage != null) {
            baseRateFire = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey("Fire"));
        }

        BigDecimal baseRateFirePremium = (baseRateFire != null) ? baseRateFire.get().baseRate() : BigDecimal.ZERO;
        // double baseRateFirePremium = _baseRateFirePremium.doubleValue();


        // Vehicle Registration State
        var vehicleRegistrationState = ECQuoteVehicle.data().vehicle_registration_state();
        // Base rate values
        Optional<VehicleRegistrationState> vehicleRegistrationStateFactor = null;
        if(fireCoverage !=null) {
            vehicleRegistrationStateFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleRegistrationState.class)
                    .getRecord(BaseRate.makeKey(vehicleRegistrationState));
        }

        BigDecimal vehicleRegistrationStateValuePremium = vehicleRegistrationStateFactor.get().fireCoverage();
        // double vehicleRegistrationStateValuePremium = _vehicleRegistrationStateValuePremium.doubleValue();

        // Vehicle Damage
        var vehicleDamage = ECQuoteVehicle.data().vehicle_damage();
        // Base rate values
        Optional<VehicleDamage> vehicleDamageFactor = null;
        if(fireCoverage !=null) {
            vehicleDamageFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleDamage.class)
                    .getRecord(BaseRate.makeKey(vehicleDamage));
        }

        BigDecimal vehicleDamageValuePremium = vehicleDamageFactor.get().fireCoverage();
        //  double vehicleDamageValuePremium = _vehicleDamageValuePremium.doubleValue();

        // Vehicle license type
        var vehicleDriverLicenseType = ECQuoteVehicle.data().drivers().get(0).driver_license_type();
        // Base rate values
        Optional<DriverLicenseType> vehicleLicenseTypeFactor = null;
        if(fireCoverage !=null) {
            vehicleLicenseTypeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseType.class)
                    .getRecord(BaseRate.makeKey(vehicleDriverLicenseType));
        }

        BigDecimal vehicleLicenseTypeValuePremium= vehicleLicenseTypeFactor.get().fireCoverage();
        //  double vehicleLicenseTypeValuePremium = _vehicleLicenseTypeValuePremium.doubleValue();


        // Excess
        var excess = ECQuoteVehicle.data().excess();
        // Base rate values
        Optional<Excess> vehicleExcess = null;
        if(fireCoverage !=null) {
            vehicleExcess = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Excess.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(excess)));
        }

        BigDecimal vehicleExcessPremium = vehicleExcess.get().fireCoverage();
        //double vehicleExcessPremium =_vehicleExcessPremium.doubleValue();

        // Vehicle Year
        var vehicleYearKey = ECQuoteVehicle.data().vehicle_year();
        // Base rate values
        Optional<VehicleYear> vehicleEYear = null;
        if(fireCoverage !=null) {
            vehicleEYear = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleYear.class)
                    .getRecord(BaseRate.makeKey(vehicleYearKey));
        }

        BigDecimal vehicleYearPremium = vehicleEYear.get().fireCoverage();
        // double vehicleYearPremium = _vehicleYearPremium.doubleValue();


        // Claims
        var claimsKey = ECQuoteVehicle.data().drivers().get(0).claims();
        // Base rate values
        Optional<Claims> vehicleDriverClaims = null;
        if(fireCoverage !=null) {
            vehicleDriverClaims = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Claims.class)
                    .getRecord(BaseRate.makeKey(claimsKey));
        }

        BigDecimal vehicleClaimsPremium = vehicleDriverClaims.get().fireCoverage();
        //  double vehicleClaimsPremium = _vehicleClaimsPremium.doubleValue();


        var vehicleUsagePerWeekKey = ECQuoteVehicle.data().vehicle_usage_per_week();
        // Base rate values
        Optional<VehicleUsagePerWeek> vehicleUsagePerWeek = null;
        if(fireCoverage !=null) {
            vehicleUsagePerWeek = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleUsagePerWeek.class)
                    .getRecord(BaseRate.makeKey(vehicleUsagePerWeekKey));
        }

        BigDecimal vehicleUsagePerWeekPremium = vehicleDriverClaims.get().fireCoverage();
        //   double vehicleUsagePerWeekPremium = _vehicleUsagePerWeekPremium.doubleValue();


        //driver_license_age

        var driverLicenseAge = ECQuoteVehicle.data().drivers().get(0).driver_license_age();
        // Base rate values
        Optional<DriverLicenseAge> driverLicenseAgeFactor = null;
        if(fireCoverage !=null) {
            driverLicenseAgeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseAge.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(driverLicenseAge)));
        }

        BigDecimal vehicleLicenseAgePremium = driverLicenseAgeFactor.get().fireCoverage();
        //    double vehicleLicenseAgePremium = _vehicleLicenseAgePremium.doubleValue();


      /*
        Base Rate*vehicle_registration_state*vehicle_damage
                *driver_license_type*excess*vehicle_year*claims*vehicle_usage_per_week*
                driver_license_age

                */
        BigDecimal firebasePremium = baseRateFirePremium.multiply(vehicleRegistrationStateValuePremium.multiply(vehicleDamageValuePremium.multiply(vehicleLicenseTypeValuePremium.multiply(vehicleExcessPremium.multiply(vehicleYearPremium.multiply(vehicleClaimsPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleLicenseAgePremium)))))))));

        double totalCoverageFire = firebasePremium.doubleValue();
        return totalCoverageFire;
    }
    private double exposureRateTheft(Personal_autoQuote personal_autoQuote) {

        var ECQuoteVehicle = personal_autoQuote.vehicle();
        var ECQuote = personal_autoQuote.data();
        // examine coverages on each exposure
        var theftCoverage = ECQuoteVehicle.theft();

        // Base rate values
        Optional<BaseRate> baseRateTheft = null; // Declare baseRateFire outside the if block

        if(theftCoverage != null) {
            baseRateTheft = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey("Theft"));
        }

        BigDecimal baseRateTheftPremium = (baseRateTheft != null) ? baseRateTheft.get().baseRate() : BigDecimal.ZERO;


        // Vehicle Registration State
        var vehicleRegistrationState = ECQuoteVehicle.data().vehicle_registration_state();
        // Vehicle Damage
        var vehicleDamage = ECQuoteVehicle.data().vehicle_damage();
        // Vehicle license type
        var vehicleDriverLicenseType = ECQuoteVehicle.data().drivers().get(0).driver_license_type();
        // Excess
        var excess = ECQuoteVehicle.data().excess();
        // Vehicle Year
        var vehicleYearKey = ECQuoteVehicle.data().vehicle_year();
        // Claims
        var claimsKey = ECQuoteVehicle.data().drivers().get(0).claims();
        // Vehicle usage per week
        var vehicleUsagePerWeekKey = ECQuoteVehicle.data().vehicle_usage_per_week();
        //driver_license_age
        var driverLicenseAge = ECQuoteVehicle.data().drivers().get(0).driver_license_age();

        // Base rate values
        Optional<VehicleRegistrationState> vehicleRegistrationStateFactor = null;
        Optional<VehicleDamage> vehicleDamageFactor = null;
        Optional<DriverLicenseType> vehicleLicenseTypeFactor = null;
        Optional<Excess> vehicleExcess = null;
        Optional<VehicleYear> vehicleEYear = null;
        Optional<Claims> vehicleDriverClaims = null;
        Optional<VehicleUsagePerWeek> vehicleUsagePerWeek = null;
        Optional<DriverLicenseAge> driverLicenseAgeFactor = null;

        if(theftCoverage !=null) {
            vehicleRegistrationStateFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleRegistrationState.class)
                    .getRecord(BaseRate.makeKey(vehicleRegistrationState));
            vehicleDamageFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleDamage.class)
                    .getRecord(BaseRate.makeKey(vehicleDamage));
            vehicleLicenseTypeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseType.class)
                    .getRecord(BaseRate.makeKey(vehicleDriverLicenseType));
            vehicleExcess = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Excess.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(excess)));
            vehicleEYear = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleYear.class)
                    .getRecord(BaseRate.makeKey(vehicleYearKey));
            vehicleDriverClaims = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Claims.class)
                    .getRecord(BaseRate.makeKey(claimsKey));
            vehicleUsagePerWeek = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleUsagePerWeek.class)
                    .getRecord(BaseRate.makeKey(vehicleUsagePerWeekKey));
            driverLicenseAgeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseAge.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(driverLicenseAge)));
        }

        BigDecimal vehicleRegistrationStateValuePremium = vehicleRegistrationStateFactor.get().theftCoverage();
        BigDecimal vehicleDamageValuePremium = vehicleDamageFactor.get().thirdPartyCoverage();
        BigDecimal vehicleLicenseTypeValuePremium= vehicleLicenseTypeFactor.get().theftCoverage();
        BigDecimal vehicleExcessPremium = vehicleExcess.get().theftCoverage();
        BigDecimal vehicleYearPremium = vehicleEYear.get().theftCoverage();
        BigDecimal vehicleClaimsPremium = vehicleDriverClaims.get().theftCoverage();
        BigDecimal vehicleUsagePerWeekPremium = vehicleUsagePerWeek.get().theftCoverage();
        BigDecimal vehicleLicenseAgePremium = driverLicenseAgeFactor.get().theftCoverage();



        BigDecimal firebasePremium = baseRateTheftPremium.multiply(vehicleRegistrationStateValuePremium.multiply(vehicleDamageValuePremium.multiply(vehicleLicenseTypeValuePremium.multiply(vehicleExcessPremium.multiply(vehicleYearPremium.multiply(vehicleClaimsPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleLicenseAgePremium)))))))));

        double totalCoverageTheft = firebasePremium.doubleValue();
        return totalCoverageTheft;
    }
    private double exposureRateThirdParty(Personal_autoQuote personal_autoQuote) {


        var ECQuoteVehicle = personal_autoQuote.vehicle();
        var ECQuote = personal_autoQuote.data();
        // examine coverages on each exposure
        var thirdPartyCoverage = ECQuoteVehicle.thirdParty();

        // Base rate values
        Optional<BaseRate> baseRateThirdParty = null; // Declare baseRateFire outside the if block

        if(thirdPartyCoverage != null) {
            baseRateThirdParty = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey("ThirdParty"));
        }

        BigDecimal baseRateTheftPremium = (baseRateThirdParty != null) ? baseRateThirdParty.get().baseRate() : BigDecimal.ZERO;


        // Vehicle Registration State
        var vehicleRegistrationState = ECQuoteVehicle.data().vehicle_registration_state();
        // Vehicle Damage
        var vehicleDamage = ECQuoteVehicle.data().vehicle_damage();
        // Vehicle license type
        var vehicleDriverLicenseType = ECQuoteVehicle.data().drivers().get(0).driver_license_type();
        // Excess
        var excess = ECQuoteVehicle.data().excess();
        // Vehicle Year
        var vehicleYearKey = ECQuoteVehicle.data().vehicle_year();
        // Claims
        var claimsKey = ECQuoteVehicle.data().drivers().get(0).claims();
        // Vehicle usage per week
        var vehicleUsagePerWeekKey = ECQuoteVehicle.data().vehicle_usage_per_week();
        //driver_license_age
        var driverLicenseAge = ECQuoteVehicle.data().drivers().get(0).driver_license_age();

        // Base rate values
        Optional<VehicleRegistrationState> vehicleRegistrationStateFactor = null;
        Optional<VehicleDamage> vehicleDamageFactor = null;
        Optional<DriverLicenseType> vehicleLicenseTypeFactor = null;
        Optional<Excess> vehicleExcess = null;
        Optional<VehicleYear> vehicleEYear = null;
        Optional<Claims> vehicleDriverClaims = null;
        Optional<VehicleUsagePerWeek> vehicleUsagePerWeek = null;
        Optional<DriverLicenseAge> driverLicenseAgeFactor = null;

        if(thirdPartyCoverage !=null) {
            vehicleRegistrationStateFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleRegistrationState.class)
                    .getRecord(BaseRate.makeKey(vehicleRegistrationState));
            vehicleDamageFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleDamage.class)
                    .getRecord(BaseRate.makeKey(vehicleDamage));
            vehicleLicenseTypeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseType.class)
                    .getRecord(BaseRate.makeKey(vehicleDriverLicenseType));
            vehicleExcess = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Excess.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(excess)));
            vehicleEYear = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleYear.class)
                    .getRecord(BaseRate.makeKey(vehicleYearKey));
            vehicleDriverClaims = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Claims.class)
                    .getRecord(BaseRate.makeKey(claimsKey));
            vehicleUsagePerWeek = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleUsagePerWeek.class)
                    .getRecord(BaseRate.makeKey(vehicleUsagePerWeekKey));
            driverLicenseAgeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseAge.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(driverLicenseAge)));
        }

        BigDecimal vehicleRegistrationStateValuePremium = vehicleRegistrationStateFactor.get().thirdPartyCoverage();
        BigDecimal vehicleDamageValuePremium = vehicleDamageFactor.get().thirdPartyCoverage();
        BigDecimal vehicleLicenseTypeValuePremium= vehicleLicenseTypeFactor.get().thirdPartyCoverage();
        BigDecimal vehicleExcessPremium = vehicleExcess.get().thirdPartyCoverage();
        BigDecimal vehicleYearPremium = vehicleEYear.get().thirdPartyCoverage();
        BigDecimal vehicleClaimsPremium = vehicleDriverClaims.get().thirdPartyCoverage();
        BigDecimal vehicleUsagePerWeekPremium = vehicleUsagePerWeek.get().thirdPartyCoverage();
        BigDecimal vehicleLicenseAgePremium = driverLicenseAgeFactor.get().thirdPartyCoverage();



        BigDecimal thirdPartyBasePremium = baseRateTheftPremium.multiply(vehicleRegistrationStateValuePremium.multiply(vehicleDamageValuePremium.multiply(vehicleLicenseTypeValuePremium.multiply(vehicleExcessPremium.multiply(vehicleYearPremium.multiply(vehicleClaimsPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleLicenseAgePremium)))))))));

        double totalCoverageThirdParty = thirdPartyBasePremium.doubleValue();
        return totalCoverageThirdParty;

    }
    private double exposureRateOwnDamage(Personal_autoQuote personal_autoQuote) {

        var ECQuoteVehicle = personal_autoQuote.vehicle();
        var ECQuote = personal_autoQuote.data();
        // examine coverages on each exposure
        var ownDamageCoverage = ECQuoteVehicle.ownDamage();

        // Base rate values
        Optional<BaseRate> baseRateOwnDamage = null; // Declare baseRateFire outside the if block

        if(ownDamageCoverage != null) {
            baseRateOwnDamage = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey("OwnDamage"));
        }

        BigDecimal baseRateOwnDamagePremium = (baseRateOwnDamage != null) ? baseRateOwnDamage.get().baseRate() : BigDecimal.ZERO;


        // Vehicle Registration State
        var vehicleRegistrationState = ECQuoteVehicle.data().vehicle_registration_state();
        // Vehicle Damage
        var vehicleDamage = ECQuoteVehicle.data().vehicle_damage();
        // Vehicle license type
        var vehicleDriverLicenseType = ECQuoteVehicle.data().drivers().get(0).driver_license_type();
        // Excess
        var excess = ECQuoteVehicle.data().excess();
        // Vehicle Year
        var vehicleYearKey = ECQuoteVehicle.data().vehicle_year();
        // Claims
        var claimsKey = ECQuoteVehicle.data().drivers().get(0).claims();
        // Vehicle usage per week
        var vehicleUsagePerWeekKey = ECQuoteVehicle.data().vehicle_usage_per_week();
        //driver_license_age
        var driverLicenseAge = ECQuoteVehicle.data().drivers().get(0).driver_license_age();

        // Base rate values
        Optional<VehicleRegistrationState> vehicleRegistrationStateFactor = null;
        Optional<VehicleDamage> vehicleDamageFactor = null;
        Optional<DriverLicenseType> vehicleLicenseTypeFactor = null;
        Optional<Excess> vehicleExcess = null;
        Optional<VehicleYear> vehicleEYear = null;
        Optional<Claims> vehicleDriverClaims = null;
        Optional<VehicleUsagePerWeek> vehicleUsagePerWeek = null;
        Optional<DriverLicenseAge> driverLicenseAgeFactor = null;

        if(ownDamageCoverage !=null) {
            vehicleRegistrationStateFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleRegistrationState.class)
                    .getRecord(BaseRate.makeKey(vehicleRegistrationState));
            vehicleDamageFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleDamage.class)
                    .getRecord(BaseRate.makeKey(vehicleDamage));
            vehicleLicenseTypeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseType.class)
                    .getRecord(BaseRate.makeKey(vehicleDriverLicenseType));
            vehicleExcess = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Excess.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(excess)));
            vehicleEYear = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleYear.class)
                    .getRecord(BaseRate.makeKey(vehicleYearKey));
            vehicleDriverClaims = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(Claims.class)
                    .getRecord(BaseRate.makeKey(claimsKey));
            vehicleUsagePerWeek = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(VehicleUsagePerWeek.class)
                    .getRecord(BaseRate.makeKey(vehicleUsagePerWeekKey));
            driverLicenseAgeFactor = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(DriverLicenseAge.class)
                    .getRecord(BaseRate.makeKey(String.valueOf(driverLicenseAge)));
        }

        BigDecimal vehicleRegistrationStateValuePremium = vehicleRegistrationStateFactor.get().ownDamageCoverage();
        BigDecimal vehicleDamageValuePremium = vehicleDamageFactor.get().ownDamageCoverage();
        BigDecimal vehicleLicenseTypeValuePremium= vehicleLicenseTypeFactor.get().ownDamageCoverage();
        BigDecimal vehicleExcessPremium = vehicleExcess.get().ownDamageCoverage();
        BigDecimal vehicleYearPremium = vehicleEYear.get().ownDamageCoverage();
        BigDecimal vehicleClaimsPremium = vehicleDriverClaims.get().ownDamageCoverage();
        BigDecimal vehicleUsagePerWeekPremium = vehicleUsagePerWeek.get().ownDamageCoverage();
        BigDecimal vehicleLicenseAgePremium = driverLicenseAgeFactor.get().ownDamageCoverage();



        BigDecimal ownDamageBasePremium = baseRateOwnDamagePremium.multiply(vehicleRegistrationStateValuePremium.multiply(vehicleDamageValuePremium.multiply(vehicleLicenseTypeValuePremium.multiply(vehicleExcessPremium.multiply(vehicleYearPremium.multiply(vehicleClaimsPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleUsagePerWeekPremium.multiply(vehicleLicenseAgePremium)))))))));

        double totalCoverageOwnDamage = ownDamageBasePremium.doubleValue();
        return totalCoverageOwnDamage;

    }

    private double exposureRateBabySeat(Personal_autoQuote personal_autoQuote) {
        var ECQuoteVehicle = personal_autoQuote.vehicle();
        var ECQuote = personal_autoQuote.data();
        // examine coverages on each exposure
        var babySeatQuoteCoverage = ECQuoteVehicle.babySeat();

        // Base rate values
        Optional<BaseRate> baseRateBabySeat = null; // Declare baseRateFire outside the if block

        if(babySeatQuoteCoverage != null) {
            baseRateBabySeat = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey("BabySeat"));
        }

        BigDecimal baseRateBabySeatPremium = (baseRateBabySeat != null) ? baseRateBabySeat.get().baseRate() : BigDecimal.ZERO;

        double totalCoverageBabySeat= baseRateBabySeatPremium.doubleValue();
        return totalCoverageBabySeat;

    }

    private double exposureRateWindscreen(Personal_autoQuote personal_autoQuote) {
        var ECQuoteVehicle = personal_autoQuote.vehicle();
        var ECQuote = personal_autoQuote.data();
        // examine coverages on each exposure
        var windscreenQuoteCoverage = ECQuoteVehicle.windscreen();

        // Base rate values
        Optional<BaseRate> baseRateWindscreen= null; // Declare baseRateFire outside the if block

        if(windscreenQuoteCoverage != null) {
            baseRateWindscreen = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey("BabySeat"));
        }

        BigDecimal baseRateRateWindscreenPremium = (baseRateWindscreen != null) ? baseRateWindscreen.get().baseRate() : BigDecimal.ZERO;

        double totalCoverageWindscreen = baseRateRateWindscreenPremium.doubleValue();
        return totalCoverageWindscreen;
    }

    private double exposureRateTransport(Personal_autoQuote personal_autoQuote) {
        var ECQuoteVehicle = personal_autoQuote.vehicle();
        var ECQuote = personal_autoQuote.data();
        // examine coverages on each exposure
        var transportQuoteCoverage = ECQuoteVehicle.transport();

        // Base rate values
        Optional<BaseRate> baseRateTransport= null; // Declare baseRateFire outside the if block

        if(transportQuoteCoverage != null) {
            baseRateTransport = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey("Transport"));
        }

        BigDecimal baseRateRateTransportPremium = (baseRateTransport != null) ? baseRateTransport.get().baseRate() : BigDecimal.ZERO;

        double totalCoverageTransport = baseRateRateTransportPremium.doubleValue();
        return totalCoverageTransport;
    }

    private double BaseExposureRate(Personal_autoQuote personal_autoQuote, String baseCoverage) {

        // Base rate values
        Optional<BaseRate> baseRate = null; // Declare baseRate outside the if block

        if(baseCoverage != null) {
            baseRate = ResourceSelectorFactory.getInstance()
                    .getSelector(personal_autoQuote)
                    .getTable(BaseRate.class)
                    .getRecord(BaseRate.makeKey(baseCoverage));
        }

        BigDecimal baseRateCoverage = (baseRate != null) ? baseRate.get().baseRate() : BigDecimal.ZERO;
        double baseRateCoveragePremium = baseRateCoverage.doubleValue();
        return baseRateCoveragePremium;
    }
    @Override
    public RatingSet rate(Personal_autoQuoteRequest request) {
        Personal_autoQuote ECQuote = request.quote();

        BigDecimal duration = request.duration();
        List<RatingItem> ratingItems = new ArrayList<>();

        // Coverages - Non-flat
        double coverageRateFire = exposureRateFire(ECQuote);
        double coverageRateTheft = exposureRateTheft(ECQuote);
        double coverageRateThirdParty = exposureRateThirdParty(ECQuote);
        double coverageRateOwnDamage = exposureRateOwnDamage(ECQuote);

        // Coverages - Non-flat
        //  double coverageRateBabySeat = BaseExposureRate(ECQuote, "BabySeat");
        //   double coverageRateWindScreen = BaseExposureRate(ECQuote,"Windscreen");
        //  double coverageRateTransport = BaseExposureRate(ECQuote,"Transport");
        double coverageRateBabySeat = exposureRateBabySeat(ECQuote);
        double coverageRateWindScreen = exposureRateWindscreen(ECQuote);
        double coverageRateTransport = exposureRateTransport(ECQuote);

        double totalPremium = coverageRateFire+coverageRateTheft+coverageRateThirdParty+coverageRateOwnDamage+coverageRateBabySeat+coverageRateWindScreen+coverageRateTransport;

        //Rate Coverage
        Fire fire = request.quote().vehicle().fire();
        BabySeat babySeat = request.quote().vehicle().babySeat();
        OwnDamage ownDamage = request.quote().vehicle().ownDamage();
        ThirdParty thirdParty = request.quote().vehicle().thirdParty();
        Theft theft = request.quote().vehicle().theft();
        Transport transport = request.quote().vehicle().transport();
        Windscreen windscreen = request.quote().vehicle().windscreen();


        // Fire Coverage
        ratingItems.add(RatingItem.builder()
                .elementLocator(fire.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(coverageRateFire))
                .build());

        // Theft
        ratingItems.add(RatingItem.builder()
                .elementLocator(theft.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(coverageRateTheft))
                .build());

        // Own Damage Coverage
        ratingItems.add(RatingItem.builder()
                .elementLocator(ownDamage.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(coverageRateOwnDamage))
                .build());


        // Third Party Coverage
        ratingItems.add(RatingItem.builder()
                .elementLocator(thirdParty.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(coverageRateThirdParty))
                .build());

        // Baby Seat
        ratingItems.add(RatingItem.builder()
                .elementLocator(babySeat.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(coverageRateBabySeat))
                .build());

        // WindScreen
        ratingItems.add(RatingItem.builder()
                .elementLocator(windscreen.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(coverageRateWindScreen))
                .build());

        // Transport
        ratingItems.add(RatingItem.builder()
                .elementLocator(transport.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(coverageRateTransport))
                .build());

        // Total Premium
        ratingItems.add(RatingItem.builder()
                .elementLocator(ECQuote.locator())
                .chargeType(ChargeType.premium)
                .rate(BigDecimal.valueOf(totalPremium))
                .build());


        return RatingSet.builder().ok(true).ratingItems(ratingItems).build();
    }
}