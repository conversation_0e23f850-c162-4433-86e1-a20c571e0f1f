package com.socotra.deployment.customer.ho3;
import com.socotra.deployment.customer.*;

import com.socotra.coremodel.UnderwritingFlagCore;
import com.socotra.coremodel.UnderwritingLevel;
import com.socotra.coremodel.UnderwritingModification;

import java.util.List;

public class Ho3UnderwritingPlugin implements UnderwritingPlugin {
    public UnderwritingModification underwrite(Ho3QuoteRequest request) {
        return UnderwritingModification.builder()
                .flagsToCreate(List.of(UnderwritingFlagCore.builder()
                        .level(UnderwritingLevel.approve)
                        .build()))
                .build();
    }
}