{"displayName": "Organisation", "abstract": false, "data": {"organisationName": {"displayName": "Organisation Name", "defaultValue": "N/A", "type": "string", "maxLength": 20000, "scope": "P"}, "organisationTradingName": {"displayName": "Trading Name", "type": "string?", "maxLength": 20000, "scope": "P"}, "organisationNZBN": {"displayName": "NZBN (New Zealand Business Number)", "type": "int?", "scope": "P"}, "organisationDateofIncorporation": {"displayName": "Date of Incorporation", "type": "date?", "scope": "P"}, "organisationContactPerson": {"displayName": "Contact Person", "type": "string?", "maxLength": 20000, "scope": "P"}, "custAcctMgmtDistributionChannel": {"displayName": "Distribution Channel", "type": "string", "maxLength": 20000, "options": ["Direct", "Broker"], "scope": "P"}, "policyHolderPreviousInsurer": {"displayName": "Previous Insurer", "type": "string?", "maxLength": 20000, "scope": "P"}, "policyHolderPreviousInsurerOther": {"displayName": "Previous Insurer - Other", "type": "string?", "maxLength": 20000, "scope": "P"}, "customerVulnerability": {"displayName": "Customer Vulnerability", "type": "CustomerVulnerability?", "scope": "P"}, "customerAccountLifecycle": {"displayName": "Customer Account Lifecycle", "type": "CustomerAccountLifecycle", "scope": "P"}, "accountBroker": {"displayName": "Account Broker", "type": "AccountBroker?", "scope": "P"}, "accountReferralPartner": {"displayName": "Referral Partner", "type": "Account<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ner?", "scope": "P"}, "contactInformation": {"displayName": "Contact Information", "type": "ContactInformation", "scope": "P"}, "customerGroup": {"type": "CustomerGroup*", "scope": "P", "displayName": "Customer Group"}}}