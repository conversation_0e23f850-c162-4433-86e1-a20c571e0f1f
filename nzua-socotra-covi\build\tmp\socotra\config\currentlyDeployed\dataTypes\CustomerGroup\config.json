{"displayName": "Customer Group", "abstract": false, "data": {"custGroupNZMCAMembership": {"displayName": "NZMCA Membership", "type": "boolean", "scope": "P"}, "custGroupNZMCAMembershipNumber": {"displayName": "NZMCA Membership Number", "type": "string?", "scope": "P", "maxLength": 20000}, "custGroupName": {"displayName": "Name of Club", "type": "string?", "scope": "P", "maxLength": 20000, "options": ["other-please specify"]}, "custGroupNameOther": {"displayName": "Name of Club - Other", "type": "string?", "scope": "P", "maxLength": 20000}, "custGroupMembershipNumber": {"displayName": "Membership Number", "type": "string?", "scope": "P", "maxLength": 20000}}}