{"displayName": "Person", "abstract": false, "data": {"personFirstName": {"displayName": "First Name", "type": "string", "scope": "P", "defaultValue": "N/A", "maxLength": 20000}, "personLastName": {"displayName": "Last Name", "type": "string", "scope": "P", "defaultValue": "N/A", "maxLength": 20000}, "personSalutation": {"displayName": "Salutation", "type": "string?", "scope": "P", "maxLength": 20000, "options": ["Dr.", "Mx.", "Ms.", "Prof.", "Mr.", "Miss.", "Mrs."]}, "personMiddleName": {"displayName": "Middle Name(s)", "type": "string?", "scope": "P", "maxLength": 20000}, "personDOB": {"displayName": "Date of Birth", "type": "date"}, "personAge": {"displayName": "Age", "type": "int", "scope": "P"}, "custAcctMgmtDistributionChannel": {"displayName": "Distribution Channel", "type": "string", "scope": "P", "maxLength": 20000, "options": ["Direct ", "Broker"]}, "policyHolderPreviousInsurer": {"displayName": "Previous Insurer", "type": "string?", "maxLength": 20000}, "policyHolderPreviousInsurerOther": {"displayName": "Previous Insurer - Other", "type": "string?", "scope": "P", "maxLength": 20000}, "customerVulnerability": {"displayName": "Customer Vulnerability", "type": "CustomerVulnerability?", "scope": "P"}, "custromerAccountLifecycle": {"displayName": "Customer Account Lifecycle", "type": "CustomerAccountLifecycle", "scope": "P"}, "accountBroker": {"displayName": "Account Broker", "type": "AccountBroker?", "scope": "P"}, "accountReferralPartner": {"displayName": "Referral Partner", "type": "Account<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ner?", "scope": "P"}, "contactInformation": {"displayName": "Contact Information", "type": "ContactInformation", "scope": "P"}, "customerGroup": {"displayName": "Customer Group", "type": "CustomerGroup*", "scope": "P"}}}