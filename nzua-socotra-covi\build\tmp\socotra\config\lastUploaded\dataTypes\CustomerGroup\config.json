{"abstract": false, "displayName": "Customer Group", "data": {"custGroupNZMCAMembership": {"displayName": "NZMCA Membership", "type": "boolean", "scope": "P"}, "custGroupNZMCAMembershipNumber": {"displayName": "NZMCA Membership Number", "type": "string?", "maxLength": 20000, "scope": "P"}, "custGroupName": {"displayName": "Name of Club", "type": "string?", "maxLength": 20000, "options": ["other-please specify"], "scope": "P"}, "custGroupNameOther": {"displayName": "Name of Club - Other", "type": "string?", "maxLength": 20000, "scope": "P"}, "custGroupMembershipNumber": {"type": "string?", "scope": "P", "maxLength": 20000, "displayName": "Membership Number"}}}