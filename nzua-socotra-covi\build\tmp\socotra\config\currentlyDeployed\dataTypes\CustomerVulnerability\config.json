{"displayName": "Customer Vulnerability", "abstract": false, "data": {"custVulnerabilityStatus": {"displayName": "Vulnerability Status", "type": "boolean", "scope": "P"}, "custVulTypeHealthAndPhysicalLD": {"displayName": "Vulnerability Type - Health and Physical Factors - Learning Disabilities", "type": "boolean?", "scope": "P"}, "custVulTypeHealthAndPhysicalMHI": {"displayName": "Vulnerability Type - Health and Physical Factors - Mental Health Issues", "type": "boolean?", "scope": "P"}, "custVulTypeHealthAndPhysicalPD": {"displayName": "Vulnerability Type - Health and Physical Factors - Physical Disabilities", "type": "boolean?", "scope": "P"}, "custVulTypeHealthAndPhysicalPHI": {"displayName": "Vulnerability Type - Health and Physical Factors - Physical Health Issues", "type": "boolean?", "scope": "P"}, "custVulTypeResilenceFS": {"displayName": "Vulnerability Type - Resilience - Financial Stress", "type": "boolean?", "scope": "P"}, "custVulTypeResilenceLOI": {"displayName": "Vulnerability Type - Resilience - Loss of Income", "type": "boolean?", "scope": "P"}, "custVulTypeLifeEventsBEOR": {"displayName": "Vulnerability Type - Life Events - Bereavement / Ending of Relationship", "type": "boolean?", "scope": "P"}, "custVulTypeLifeEventsCR": {"displayName": "Vulnerability Type - Life Events - Caring Responsibilities", "type": "boolean?", "scope": "P"}, "custVulTypeLifeEventsND": {"displayName": "Vulnerability Type - Life Events - Natural Disasters", "type": "boolean?", "scope": "P"}, "custVulTypeLifeEventsRM": {"displayName": "Vulnerability Type - Life Events - Recently Migrated", "type": "boolean?", "scope": "P"}, "custVulTypeCapabilityDE": {"displayName": "Vulnerability Type - Capability - Digital Exclusion", "type": "boolean?", "scope": "P"}, "custVulTypeCapabilityEAASL": {"displayName": "Vulnerability Type - Capability - English as a Second Language", "type": "boolean?", "scope": "P"}, "custVulTypeCapabilityLLOFC": {"displayName": "Vulnerability Type - Capability - Low Level of Financial Capability", "type": "boolean?", "scope": "P"}, "custVulTypeCapabilityLLL": {"displayName": "Vulnerability Type - Capability - Low Literacy Levels", "type": "boolean?", "scope": "P"}, "custVulnerabilityNote": {"displayName": "Vulberability Note", "type": "string?", "scope": "P", "maxLength": 20000}}}