package com.socotra.deployment.customer.personal_auto;
import com.socotra.deployment.customer.*;

import com.socotra.coremodel.*;
import com.socotra.deployment.*;
import com.socotra.platform.tools.ULID;
import com.socotra.coremodel.DocumentDataSnapshot;

import java.util.*;
import java.math.*;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AutoDocDataSnapshotPlugin implements DocumentDataSnapshotPlugin {
    private static final Logger log = LoggerFactory.getLogger(AutoDocDataSnapshotPlugin.class);

    @Override
    public DocumentDataSnapshot dataSnapshot(DocumentDataSnapshotPlugin.Personal_autoQuoteRequest request) {
        log.info("In dataSnapshot plugin");

        return DocumentDataSnapshot.builder()
                .renderingData(request.quote())
                .build();
    }

}