package com.socotra.deployment.customer.ho3;
import com.socotra.deployment.customer.*;
import com.socotra.coremodel.DurationBasis;
import com.socotra.coremodel.RatingItem;
import com.socotra.coremodel.RatingSet;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
public class Ho3RatingPlugin implements RatePlugin {
    @Override
    public RatingSet rate(Ho3QuoteRequest request) {
        Ho3Quote ho3Quote = request.quote();
        BigDecimal duration = request.duration();
        List<RatingItem> ratingItems = new ArrayList<>();

        //Rate Coverage A
        Coverage_a coverage_a = request.quote().dwelling().coverage_a();
        double coverage_a_rate = 0.0015 * coverage_a.coverage_a_limit().value().doubleValue() / 120;
        ratingItems.add(RatingItem.builder()
                .elementLocator(coverage_a.locator())
                .chargeType(ChargeType.coverage_a_premium)
                .rate(BigDecimal.valueOf(coverage_a_rate))
                .build());

        //Rate Coverage B
        Coverage_b coverage_b = request.quote().dwelling().coverage_b();
        double coverage_b_rate = coverage_a_rate * (coverage_b.coverage_b_limit().value().doubleValue()/100);
        ratingItems.add(RatingItem.builder()
                .elementLocator(coverage_b.locator())
                .chargeType(ChargeType.coverage_b_premium)
                .rate(BigDecimal.valueOf(coverage_b_rate))
                .build());
        
        //Rate Water Backup if not null
        if (request.quote().dwelling().water_backup() != null) {
            Water_backup water_backup = request.quote().dwelling().water_backup();
            double water_backup_rate = coverage_a_rate * (water_backup.water_backup_limit().value().doubleValue()/100);
            ratingItems.add(RatingItem.builder()
                    .elementLocator(water_backup.locator())
                    .chargeType(ChargeType.coverage_a_premium)
                    .rate(BigDecimal.valueOf(water_backup_rate))
                    .build());
        }

        
        //Rate Personal Property if not null
        if (request.quote().dwelling().personal_property() != null) {
            Personal_property personal_property = request.quote().dwelling().personal_property();
            double personal_property_rate = coverage_a_rate * 0.075;
            ratingItems.add(RatingItem.builder()
                    .elementLocator(personal_property.locator())
                    .chargeType(ChargeType.coverage_a_premium)
                    .rate(BigDecimal.valueOf(personal_property_rate))
                    .build());

        }

        return RatingSet.builder().ok(true).ratingItems(ratingItems).build();
    }
}