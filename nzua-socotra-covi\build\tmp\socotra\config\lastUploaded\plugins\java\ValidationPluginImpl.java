package com.socotra.deployment.customer;

import com.socotra.coremodel.ValidationItem;

public class ValidationPluginImpl implements ValidationPlugin {
 @Override
 public ValidationItem validate(PersonAccountRequest request) {
   PersonAccount personAccount = request.account();

    // Check if the last name is "<PERSON>"
   if (personAccount.data().personLastName().equalsIgnoreCase("Batman")) {
     return ValidationItem.builder()
         .addError("A fictional person cannot be insured")    //error message does not show up in the UI
         .build();
   }


   return ValidationItem.builder().build();
 }
}