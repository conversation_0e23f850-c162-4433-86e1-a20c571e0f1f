package com.socotra.deployment.customer.ho3;
import com.socotra.deployment.customer.*;

import com.socotra.coremodel.ValidationItem;
import com.socotra.deployment.customer.ValidationPlugin;

public class Ho3validationPlugin implements ValidationPlugin {
    @Override
    public ValidationItem validate(Ho3QuoteRequest request){
        Ho3Quote ho3Quote = request.quote();

        return ValidationItem.builder().build();
    }
}
