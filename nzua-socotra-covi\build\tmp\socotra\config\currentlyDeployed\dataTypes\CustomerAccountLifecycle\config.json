{"displayName": "Customer Account Lifecycle", "abstract": false, "data": {"custAcctMgmtCustomerStatus": {"displayName": "Customer Status", "type": "string", "scope": "P", "maxLength": 20000, "options": ["Prospect", "Active", "Inactive", "Blacklist"]}, "custAcctMgmtInactiveReason": {"displayName": "Customer Inactive Reason", "type": "string?", "scope": "P", "maxLength": 20000}, "custAcctMgmtBlacklistReason": {"displayName": "Customer Blacklist Reason", "type": "string?", "scope": "P", "maxLength": 20000}}}