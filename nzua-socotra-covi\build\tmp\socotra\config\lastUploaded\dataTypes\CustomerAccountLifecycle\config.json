{"abstract": false, "displayName": "Customer Account Lifecycle", "data": {"custAcctMgmtCustomerStatus": {"displayName": "Customer Status", "type": "string", "options": ["Prospect", "Active", "Inactive", "Blacklist"], "scope": "P"}, "custAcctMgmtInactiveReason": {"displayName": "Customer Inactive Reason", "type": "string?", "maxLength": 20000, "scope": "P"}, "custAcctMgmtBlacklistReason": {"displayName": "Customer Blacklist Reason", "type": "string?", "maxLength": 20000, "scope": "P"}}}