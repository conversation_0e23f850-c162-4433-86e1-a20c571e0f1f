package com.socotra.deployment.customer;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;

public class ValidationPluginTest {

    @Test
    public void testValidationPluginExists(){
        // Simple test to verify the ValidationPluginImpl class exists and can be instantiated
        var validationPlugin = new ValidationPluginImpl();
        Assertions.assertNotNull(validationPlugin, "ValidationPluginImpl should be instantiable");
    }

    @Test
    public void testValidationLogicDirectly(){
        // Test the validation logic directly by checking the string comparison
        String batmanName = "Batman";
        String smithName = "Smith";

        // Test the core logic that's in the validate method
        boolean batmanShouldFail = batmanName.equalsIgnoreCase("Batman");
        boolean smithShouldPass = !smithName.equalsIgnoreCase("Batman");

        Assertions.assertTrue(batmanShouldFail, "Batman should trigger validation failure");
        Assertions.assertTrue(smithShouldPass, "<PERSON> should pass validation");
    }
}
