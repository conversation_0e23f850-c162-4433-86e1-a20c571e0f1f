package com.socotra.deployment.customer.personal_auto;
import com.socotra.deployment.customer.*;
import com.socotra.coremodel.QuotePricing;
import com.socotra.coremodel.ValidationItem;
import com.socotra.deployment.DataFetcher;
import com.socotra.platform.tools.ULID;
import java.util.*;



public class AutoValidationPluginImpl implements ValidationPlugin {
  @Override
  public ValidationItem validate(CommercialAccountRequest request) {

    CommercialAccount account = request.account();

    String companyName = account.data().companyName();
    String fein  = account.data().fein();

    if (companyName ==null || companyName.isEmpty()) {
      return ValidationItem.builder()
              .addError("a companyName name cannot be empty")
              .build();
    }
    if (fein ==null || fein.isEmpty()) {
      return ValidationItem.builder()
              .addError("a fein ID cannot be empty")
              .build();
    }

    return ValidationItem.builder().build();
  }

  public ValidationItem validate(Personal_autoQuoteRequest request) {

    var ECQuote = request.quote();
    var vq = ECQuote.vehicle();
    var vehicleQuoteData = vq.data();
    var motorData = ECQuote.data();

    // Motor Product Data
    List<Policyholder> policyholders = motorData.policyholder();

    // Vehicle Exposure Information
    String vehicle_year = vehicleQuoteData.vehicle_year();

    if(vehicle_year ==null || Integer.parseInt(vehicle_year) ==0){
      return ValidationItem.builder()
              .addError("Vehicle year is a mandatory field")
              .build();
    }

    if (policyholders == null || policyholders.isEmpty()) {


      return ValidationItem.builder()
              .addError("At Least one policyholder should be added onto the policy")
              .build();

    }

    return ValidationItem.builder().build();
  }

}
